#!/usr/bin/env python3
"""
快速测试cookies有效性
"""

import asyncio
import json
import os
from twikit import Client

# 处理代理设置
def get_proxy_config():
    """获取并转换代理配置"""
    proxy_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'all_proxy', 'ALL_PROXY']

    for var in proxy_vars:
        proxy_url = os.environ.get(var)
        if proxy_url:
            print(f"🔧 检测到代理: {var}={proxy_url}")

            # 转换socks代理格式
            if proxy_url.startswith('socks://'):
                proxy_url = proxy_url.replace('socks://', 'socks5://')
                print(f"🔄 转换代理格式: {proxy_url}")

            return proxy_url

    print("🔧 未检测到代理设置")
    return None

async def test_cookies():
    """测试cookies"""
    print("🧪 Cookies测试工具")
    print("=" * 30)
    
    # 检查文件
    if not os.path.exists('cookies.json'):
        print("❌ 找不到 cookies.json 文件")
        return
    
    # 读取cookies
    try:
        with open('cookies.json', 'r', encoding='utf-8') as f:
            cookies = json.load(f)
        print(f"✅ 成功读取cookies文件")
        print(f"📊 包含字段: {list(cookies.keys())}")
        
        # 检查必要字段
        if 'auth_token' in cookies:
            auth_token = cookies['auth_token']
            print(f"🔑 auth_token: {auth_token[:20]}...{auth_token[-10:] if len(auth_token) > 30 else auth_token}")
        else:
            print("❌ 缺少 auth_token")
            return
            
        if 'ct0' in cookies:
            ct0 = cookies['ct0']
            print(f"🛡️ ct0: {ct0[:20]}...{ct0[-10:] if len(ct0) > 30 else ct0}")
        else:
            print("⚠️ 缺少 ct0 (可选)")
            
    except Exception as e:
        print(f"❌ 读取cookies失败: {e}")
        return
    
    # 测试登录
    try:
        print("\n🔧 创建客户端...")
        proxy_config = get_proxy_config()

        if proxy_config:
            client = Client('en-US', proxy=proxy_config)
            print(f"✅ 客户端创建成功 (使用代理: {proxy_config})")
        else:
            client = Client('en-US')
            print("✅ 客户端创建成功 (直连)")

        client.set_cookies(cookies)
        print("✅ 已设置cookies")
        
        print("\n🧪 测试1: Settings API")
        try:
            response, _ = await client.v11.settings()
            if isinstance(response, dict) and 'screen_name' in response:
                print(f"✅ Settings API成功: @{response['screen_name']}")
            else:
                print(f"📡 Settings API响应: {type(response)}")
        except Exception as e:
            print(f"❌ Settings API异常: {e}")

        print("\n🧪 测试2: 搜索推文")
        try:
            tweets = await client.search_tweet('hello', 'Latest', count=1)
            if tweets and len(tweets) > 0:
                print(f"✅ 搜索推文成功: 找到 {len(tweets)} 条")
                print(f"   示例: {tweets[0].text[:50]}...")
            else:
                print("❌ 搜索推文失败: 无结果")
        except Exception as e:
            print(f"❌ 搜索推文异常: {e}")
            
        print("\n🧪 测试3: 获取推文")
        try:
            tweets = await client.search_tweet('hello', 'Latest', count=1)
            if tweets:
                print(f"✅ 搜索推文成功: 找到 {len(tweets)} 条")
            else:
                print("❌ 搜索推文失败: 无结果")
        except Exception as e:
            print(f"❌ 搜索推文异常: {e}")
            
    except Exception as e:
        print(f"❌ 客户端创建失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_cookies())
