#!/usr/bin/env python3
"""
安全的Twitter推文抓取工具
支持多账号管理、防封号保护、智能重试等功能
"""

import asyncio
import json
import os
import time
import random
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('twitter_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SafeTwitterScraper:
    """安全的Twitter推文抓取器"""

    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.current_account = None
        self.client = None
        self.is_logged_in = False

        # 安全参数
        self.rate_limits = {
            'user_tweets': {'limit': 50, 'window': 900},  # 50次/15分钟
            'user_info': {'limit': 95, 'window': 900},    # 95次/15分钟
            'login': {'limit': 187, 'window': 900}        # 187次/15分钟
        }

        self.request_history = {
            'user_tweets': [],
            'user_info': [],
            'login': []
        }

    def _load_config(self) -> dict:
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            self._create_default_config()

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            return self._get_default_config()

    def _create_default_config(self):
        """创建默认配置文件"""
        default_config = self._get_default_config()

        print("🔧 首次运行，创建配置文件...")
        print("=" * 60)
        print("📋 支持两种登录方式:")
        print("1. 🔑 账号密码登录 (传统方式)")
        print("2. 🍪 浏览器Cookies登录 (推荐，更安全)")
        print("=" * 60)

        choice = input("请选择登录方式 (1/2) [2]: ").strip() or "2"

        if choice == "1":
            # 传统账号密码方式
            print("\n📝 请输入Twitter账号信息:")
            username = input("用户名: ").strip()
            email = input("邮箱: ").strip()
            password = input("密码: ").strip()

            if username and email and password:
                account = {
                    "name": f"{username}_account",
                    "username": username,
                    "email": email,
                    "password": password,
                    "cookies_file": f"cookies_{username}.json",
                    "auth_method": "password",
                    "enabled": True,
                    "last_used": None,
                    "request_count": 0
                }
                default_config['accounts'].append(account)
        else:
            # 浏览器Cookies方式
            account = self._setup_browser_auth()
            if account:
                default_config['accounts'].append(account)

        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)

        print(f"✅ 配置文件已创建: {self.config_file}")
        return default_config

    def _setup_browser_auth(self) -> dict:
        """设置浏览器授权"""
        print("\n🍪 浏览器Cookies登录设置")
        print("=" * 50)
        print("📋 步骤说明:")
        print("1. 在浏览器中登录 Twitter")
        print("2. 导出cookies (推荐使用浏览器扩展)")
        print("3. 将cookies保存为JSON文件")
        print("4. 输入cookies文件路径")
        print("=" * 50)

        # 显示cookies导出指南
        self._show_cookie_export_guide()

        account_name = input("\n账号名称 (用于识别): ").strip()
        if not account_name:
            account_name = "browser_account"

        cookies_file = input("Cookies文件路径 [cookies.json]: ").strip() or "cookies.json"

        # 验证cookies文件
        if os.path.exists(cookies_file):
            if self._validate_cookies_file(cookies_file):
                print("✅ Cookies文件验证成功")
                return {
                    "name": account_name,
                    "username": "browser_user",  # 将在登录时自动获取
                    "auth_method": "cookies",
                    "cookies_file": cookies_file,
                    "enabled": True,
                    "last_used": None,
                    "request_count": 0
                }
            else:
                print("❌ Cookies文件格式无效")
        else:
            print(f"❌ Cookies文件不存在: {cookies_file}")
            print("请先导出cookies文件后重新运行程序")

        return None

    def _show_cookie_export_guide(self):
        """显示cookies导出指南"""
        print("\n📖 Cookies导出指南:")
        print("=" * 40)
        print("🔧 方法一: 使用浏览器扩展 (推荐)")
        print("  - Chrome: 'Get cookies.txt LOCALLY'")
        print("  - Firefox: 'cookies.txt'")
        print("  - 导出格式选择: JSON")
        print("")
        print("🔧 方法二: 手动导出")
        print("  1. 打开 https://twitter.com")
        print("  2. 按F12打开开发者工具")
        print("  3. 进入Application/Storage -> Cookies")
        print("  4. 复制所有cookies到JSON文件")
        print("")
        print("📄 JSON格式示例:")
        print('  {"auth_token": "xxx", "ct0": "xxx", ...}')
        print("=" * 40)

    def _validate_cookies_file(self, cookies_file: str) -> bool:
        """验证cookies文件格式"""
        try:
            with open(cookies_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)

            # 检查必需的cookies - 主要是auth_token
            if isinstance(cookies, dict):
                has_auth_token = 'auth_token' in cookies
                has_ct0 = 'ct0' in cookies

                if has_auth_token:
                    if has_ct0:
                        print("✅ 完美！包含 auth_token 和 ct0")
                    else:
                        print("✅ 包含 auth_token (通常足够)")
                    return True
                else:
                    print("❌ 缺少关键的 auth_token")
                    return False

            elif isinstance(cookies, list):
                # 处理数组格式的cookies
                cookie_names = [cookie.get('name', '') for cookie in cookies if isinstance(cookie, dict)]
                has_auth_token = 'auth_token' in cookie_names

                if has_auth_token:
                    print("✅ 检测到 auth_token")
                    return True
                else:
                    print("❌ 缺少关键的 auth_token")
                    return False

            return False
        except Exception as e:
            logger.error(f"验证cookies文件失败: {e}")
            return False

    def _get_default_config(self) -> dict:
        """获取默认配置"""
        return {
            "accounts": [],
            "scraping_settings": {
                "default_delay": 3.0,
                "min_delay": 2.0,
                "max_delay": 5.0,
                "max_retries": 3,
                "batch_size": 20,
                "enable_random_delay": True,
                "respect_rate_limits": True
            },
            "safety_settings": {
                "max_tweets_per_session": 1000,
                "max_users_per_day": 10,
                "cooldown_after_error": 300,
                "auto_switch_account": True
            },
            "output_settings": {
                "default_format": "json",
                "output_directory": "output",
                "include_statistics": True,
                "compress_output": False
            }
        }

    def _check_rate_limit(self, operation: str) -> Tuple[bool, float]:
        """检查是否超过速率限制"""
        if not self.config['scraping_settings']['respect_rate_limits']:
            return True, 0

        if operation not in self.rate_limits:
            return True, 0

        limit_info = self.rate_limits[operation]
        current_time = time.time()
        window_start = current_time - limit_info['window']

        # 清理过期的请求记录
        self.request_history[operation] = [
            req_time for req_time in self.request_history[operation]
            if req_time > window_start
        ]

        current_count = len(self.request_history[operation])

        if current_count >= limit_info['limit']:
            # 计算需要等待的时间
            oldest_request = min(self.request_history[operation])
            wait_time = oldest_request + limit_info['window'] - current_time
            return False, max(0, wait_time)

        return True, 0

    def _record_request(self, operation: str):
        """记录请求时间"""
        self.request_history[operation].append(time.time())

    async def _safe_delay(self):
        """安全延迟"""
        settings = self.config['scraping_settings']

        if settings['enable_random_delay']:
            delay = random.uniform(settings['min_delay'], settings['max_delay'])
        else:
            delay = settings['default_delay']

        logger.debug(f"等待 {delay:.1f} 秒...")
        await asyncio.sleep(delay)

    async def login(self, account_name: str = None) -> bool:
        """安全登录 - 支持密码和cookies两种方式"""
        from twikit import Client

        # 选择账号
        if account_name:
            account = next((acc for acc in self.config['accounts']
                          if acc['name'] == account_name), None)
        else:
            # 选择第一个可用账号
            account = next((acc for acc in self.config['accounts']
                          if acc.get('enabled', True)), None)

        if not account:
            logger.error("没有可用的账号")
            return False

        auth_method = account.get('auth_method', 'password')
        logger.info(f"🔐 使用 {auth_method} 方式登录账号: {account['name']}")

        # 检查登录速率限制
        can_login, wait_time = self._check_rate_limit('login')
        if not can_login:
            logger.warning(f"登录速率限制，需要等待 {wait_time:.1f} 秒")
            await asyncio.sleep(wait_time)

        try:
            self.client = Client('en-US')
            cookies_file = account.get('cookies_file', 'cookies.json')

            if auth_method == 'cookies':
                # 纯cookies登录方式
                return await self._login_with_cookies(account, cookies_file)
            else:
                # 传统密码登录方式（带cookies缓存）
                return await self._login_with_password(account, cookies_file)

        except Exception as e:
            logger.error(f"❌ 登录失败: {e}")
            return False

    async def _login_with_cookies(self, account: dict, cookies_file: str) -> bool:
        """使用cookies登录"""
        if not os.path.exists(cookies_file):
            logger.error(f"❌ Cookies文件不存在: {cookies_file}")
            return False

        try:
            logger.info("🍪 加载cookies文件...")

            # 加载并处理cookies
            cookies = self._load_and_process_cookies(cookies_file)
            if not cookies:
                logger.error("❌ Cookies文件格式无效")
                return False

            self.client.set_cookies(cookies)

            # 验证cookies有效性
            logger.info("🔍 验证cookies有效性...")
            user_info = await self._verify_cookies()

            if user_info:
                logger.info(f"✅ Cookies登录成功! 用户: @{user_info['screen_name']}")

                # 更新账号信息
                account['username'] = user_info['screen_name']
                account['user_id'] = user_info['id']
                account['last_used'] = datetime.now().isoformat()
                account['request_count'] = account.get('request_count', 0) + 1

                self.current_account = account
                self.is_logged_in = True
                self._save_config()

                # 保存更新后的cookies
                self.client.save_cookies(cookies_file)
                return True
            else:
                logger.error("❌ Cookies已失效，请重新导出")
                return False

        except Exception as e:
            logger.error(f"❌ Cookies登录失败: {e}")
            return False

    async def _login_with_password(self, account: dict, cookies_file: str) -> bool:
        """使用密码登录（带cookies缓存）"""
        # 先尝试使用已保存的cookies
        if os.path.exists(cookies_file):
            logger.info("🍪 尝试使用保存的cookies登录...")
            try:
                self.client.load_cookies(cookies_file)
                user_info = await self._verify_cookies()
                if user_info:
                    logger.info("✅ Cookies登录成功")
                    self.current_account = account
                    self.is_logged_in = True
                    return True
            except Exception:
                logger.info("🔑 Cookies已过期，使用账号密码登录...")

        # 使用账号密码登录
        if not all([account.get('username'), account.get('email'), account.get('password')]):
            logger.error("❌ 账号信息不完整")
            return False

        self._record_request('login')
        await self.client.login(
            auth_info_1=account['username'],
            auth_info_2=account['email'],
            password=account['password'],
            cookies_file=cookies_file,
            enable_ui_metrics=True  # 减少封号风险
        )

        logger.info("✅ 账号密码登录成功")
        self.current_account = account
        self.is_logged_in = True

        # 更新账号使用记录
        account['last_used'] = datetime.now().isoformat()
        account['request_count'] = account.get('request_count', 0) + 1
        self._save_config()

        return True

    def _load_and_process_cookies(self, cookies_file: str) -> dict:
        """加载并处理cookies文件"""
        try:
            with open(cookies_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 处理不同格式的cookies
            if isinstance(data, dict):
                # 直接的键值对格式
                return data
            elif isinstance(data, list):
                # 数组格式，转换为键值对
                cookies = {}
                for item in data:
                    if isinstance(item, dict) and 'name' in item and 'value' in item:
                        cookies[item['name']] = item['value']
                return cookies
            else:
                logger.error("❌ 不支持的cookies格式")
                return {}

        except Exception as e:
            logger.error(f"❌ 读取cookies文件失败: {e}")
            return {}

    async def _verify_cookies(self) -> dict:
        """验证cookies并获取用户信息"""
        try:
            # 尝试获取当前用户信息
            response, _ = await self.client.gql.viewer()
            user_data = response.get('data', {}).get('viewer', {})

            if user_data and user_data.get('id'):
                return {
                    'id': user_data['id'],
                    'screen_name': user_data.get('legacy', {}).get('screen_name', 'unknown'),
                    'name': user_data.get('legacy', {}).get('name', 'unknown')
                }
            return None
        except Exception as e:
            logger.debug(f"验证cookies失败: {e}")
            return None

    def _save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存配置失败: {e}")

    async def get_user_tweets_safe(self,
                                 user_id: str,
                                 tweet_type: str = 'Tweets',
                                 max_tweets: int = 500,
                                 start_date: str = None,
                                 end_date: str = None) -> List[Dict]:
        """安全获取用户推文"""
        if not self.is_logged_in:
            logger.error("请先登录")
            return []

        # 检查速率限制
        can_request, wait_time = self._check_rate_limit('user_tweets')
        if not can_request:
            logger.warning(f"推文获取速率限制，需要等待 {wait_time:.1f} 秒")
            await asyncio.sleep(wait_time)

        tweets_data = []
        retry_count = 0
        max_retries = self.config['scraping_settings']['max_retries']

        while retry_count <= max_retries:
            try:
                # 获取用户信息
                self._record_request('user_info')
                try:
                    user = await self.client.get_user_by_screen_name(user_id)
                except:
                    user = await self.client.get_user_by_id(user_id)

                logger.info(f"📋 开始爬取 @{user.screen_name} 的{tweet_type}...")

                # 获取推文
                self._record_request('user_tweets')
                tweets = await self.client.get_user_tweets(user.id, tweet_type, count=20)

                tweet_count = 0
                batch_count = 0
                batch_size = self.config['scraping_settings']['batch_size']

                while tweets and tweet_count < max_tweets:
                    for tweet in tweets:
                        # 时间过滤逻辑
                        tweet_time = tweet.created_at
                        if isinstance(tweet_time, str):
                            tweet_time = datetime.fromisoformat(tweet_time.replace('Z', '+00:00'))

                        # 提取推文数据
                        tweet_data = {
                            'id': tweet.id,
                            'text': tweet.text,
                            'created_at': tweet_time.isoformat(),
                            'user_id': user.id,
                            'user_name': user.name,
                            'user_screen_name': user.screen_name,
                            'retweet_count': getattr(tweet, 'retweet_count', 0),
                            'favorite_count': getattr(tweet, 'favorite_count', 0),
                            'reply_count': getattr(tweet, 'reply_count', 0),
                            'lang': getattr(tweet, 'lang', 'unknown'),
                            'url': f"https://twitter.com/{user.screen_name}/status/{tweet.id}"
                        }

                        tweets_data.append(tweet_data)
                        tweet_count += 1

                        if tweet_count >= max_tweets:
                            break

                    # 批次处理和延迟
                    batch_count += 1
                    if batch_count % batch_size == 0:
                        logger.info(f"📊 已获取 {tweet_count} 条推文，暂停片刻...")
                        await self._safe_delay()

                    # 获取下一页
                    if tweet_count < max_tweets:
                        try:
                            await self._safe_delay()
                            tweets = await tweets.next()
                        except Exception as e:
                            logger.warning(f"⚠️ 无法获取更多推文: {e}")
                            break

                logger.info(f"🎉 爬取完成，共获取 {len(tweets_data)} 条推文")
                return tweets_data

            except Exception as e:
                retry_count += 1
                logger.error(f"❌ 获取推文失败 (尝试 {retry_count}/{max_retries + 1}): {e}")

                if retry_count <= max_retries:
                    cooldown = self.config['safety_settings']['cooldown_after_error']
                    logger.info(f"⏰ 等待 {cooldown} 秒后重试...")
                    await asyncio.sleep(cooldown)
                else:
                    logger.error("❌ 达到最大重试次数，放弃获取")
                    break

        return tweets_data

    def save_tweets(self, tweets_data: List[Dict], user_id: str, format_type: str = 'json'):
        """保存推文数据"""
        if not tweets_data:
            logger.warning("没有数据可保存")
            return

        # 创建输出目录
        output_dir = self.config['output_settings']['output_directory']
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        base_filename = f"{user_id}_{timestamp}"

        if format_type == 'json':
            filepath = os.path.join(output_dir, f"{base_filename}.json")
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(tweets_data, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"💾 JSON文件已保存: {filepath}")

        elif format_type == 'csv':
            import pandas as pd
            filepath = os.path.join(output_dir, f"{base_filename}.csv")
            df = pd.DataFrame(tweets_data)
            df.to_csv(filepath, index=False, encoding='utf-8')
            logger.info(f"💾 CSV文件已保存: {filepath}")

        elif format_type == 'excel':
            import pandas as pd
            filepath = os.path.join(output_dir, f"{base_filename}.xlsx")
            df = pd.DataFrame(tweets_data)
            df.to_excel(filepath, index=False, engine='openpyxl')
            logger.info(f"💾 Excel文件已保存: {filepath}")

        # 保存统计信息
        if self.config['output_settings']['include_statistics']:
            stats_file = os.path.join(output_dir, f"{base_filename}_stats.json")
            stats = self._generate_statistics(tweets_data)
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"📈 统计文件已保存: {stats_file}")

    def _generate_statistics(self, tweets_data: List[Dict]) -> Dict:
        """生成统计信息"""
        if not tweets_data:
            return {}

        total_tweets = len(tweets_data)
        total_favorites = sum(t.get('favorite_count', 0) for t in tweets_data)
        total_retweets = sum(t.get('retweet_count', 0) for t in tweets_data)
        total_replies = sum(t.get('reply_count', 0) for t in tweets_data)

        # 语言统计
        languages = {}
        for tweet in tweets_data:
            lang = tweet.get('lang', 'unknown')
            languages[lang] = languages.get(lang, 0) + 1

        return {
            'total_tweets': total_tweets,
            'date_range': {
                'start': tweets_data[-1]['created_at'] if tweets_data else None,
                'end': tweets_data[0]['created_at'] if tweets_data else None
            },
            'engagement': {
                'total_favorites': total_favorites,
                'total_retweets': total_retweets,
                'total_replies': total_replies,
                'avg_favorites': total_favorites / total_tweets if total_tweets > 0 else 0,
                'avg_retweets': total_retweets / total_tweets if total_tweets > 0 else 0
            },
            'languages': languages,
            'generated_at': datetime.now().isoformat()
        }

async def interactive_mode():
    """交互式模式"""
    print("🐦 安全Twitter推文抓取工具")
    print("=" * 60)
    print("✨ 特性: 防封号保护 | 智能重试 | 多格式导出 | 统计分析")
    print("=" * 60)

    scraper = SafeTwitterScraper()

    # 显示账号信息
    accounts = scraper.config.get('accounts', [])
    if not accounts:
        print("❌ 没有配置账号，请先运行配置向导")
        return

    print(f"\n📋 可用账号: {len(accounts)} 个")
    for i, acc in enumerate(accounts, 1):
        status = "✅" if acc.get('enabled', True) else "❌"
        print(f"  {i}. {status} {acc['name']} (@{acc['username']})")

    # 登录
    print(f"\n🔐 正在登录...")
    if not await scraper.login():
        print("❌ 登录失败")
        return

    print(f"✅ 登录成功，当前账号: {scraper.current_account['name']}")

    # 获取用户输入
    print(f"\n📝 请输入抓取参数:")
    user_id = input("目标用户ID或用户名: ").strip()
    if not user_id:
        print("❌ 用户ID不能为空")
        return

    tweet_type = input("推文类型 (Tweets/Replies/Media/Likes) [Tweets]: ").strip() or "Tweets"

    try:
        max_tweets = int(input("最大推文数量 [500]: ").strip() or "500")
        max_tweets = min(max_tweets, scraper.config['safety_settings']['max_tweets_per_session'])
    except ValueError:
        max_tweets = 500

    output_format = input("输出格式 (json/csv/excel) [json]: ").strip() or "json"

    print(f"\n🚀 开始安全抓取...")
    print(f"   目标: @{user_id}")
    print(f"   类型: {tweet_type}")
    print(f"   数量: {max_tweets}")
    print(f"   格式: {output_format}")

    # 开始抓取
    start_time = time.time()
    tweets = await scraper.get_user_tweets_safe(
        user_id=user_id,
        tweet_type=tweet_type,
        max_tweets=max_tweets
    )

    if not tweets:
        print("⚠️ 没有获取到推文")
        return

    # 保存数据
    scraper.save_tweets(tweets, user_id, output_format)

    # 显示结果
    elapsed_time = time.time() - start_time
    print(f"\n✅ 抓取完成!")
    print(f"   📊 获取推文: {len(tweets)} 条")
    print(f"   ⏱️ 用时: {elapsed_time:.1f} 秒")
    print(f"   💾 文件保存在: {scraper.config['output_settings']['output_directory']}")

if __name__ == "__main__":
    try:
        asyncio.run(interactive_mode())
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        logger.error(f"程序异常: {e}")
        print(f"❌ 程序出错: {e}")