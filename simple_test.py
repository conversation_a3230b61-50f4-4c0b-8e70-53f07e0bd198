#!/usr/bin/env python3
"""
超简单的cookies测试
"""

import asyncio
import json
import os
from twikit import Client

# 处理代理设置
def get_proxy():
    """获取代理配置"""
    proxy_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'all_proxy', 'ALL_PROXY']

    for var in proxy_vars:
        proxy_url = os.environ.get(var)
        if proxy_url:
            print(f"🔧 使用代理: {proxy_url}")
            # 转换socks代理格式
            if proxy_url.startswith('socks://'):
                proxy_url = proxy_url.replace('socks://', 'socks5://')
                print(f"🔄 转换为: {proxy_url}")
            return proxy_url
    return None

async def simple_test():
    print("🧪 超简单cookies测试")
    print("=" * 30)
    
    # 检查cookies文件
    if not os.path.exists('cookies.json'):
        print("❌ 找不到 cookies.json")
        return
    
    # 读取cookies
    with open('cookies.json', 'r') as f:
        cookies = json.load(f)
    
    print(f"✅ 读取cookies: {list(cookies.keys())}")
    
    # 创建客户端 - 使用系统代理
    proxy = get_proxy()
    if proxy:
        client = Client('en-US', proxy=proxy)
        print(f"✅ 创建客户端 (代理: {proxy})")
    else:
        client = Client('en-US')
        print("✅ 创建客户端 (无代理)")

    client.set_cookies(cookies)
    print("✅ 设置cookies完成")
    
    # 最简单的测试 - 搜索推文
    try:
        print("🔍 测试搜索推文...")
        tweets = await client.search_tweet('hello', 'Latest', count=1)
        
        if tweets:
            print(f"✅ 成功! 找到 {len(tweets)} 条推文")
            print(f"   示例: {tweets[0].text[:50]}...")
            return True
        else:
            print("❌ 搜索无结果")
            return False
            
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(simple_test())
    if result:
        print("\n🎉 Cookies有效! 可以使用browser_auth_scraper.py")
    else:
        print("\n💡 建议:")
        print("1. 重新导出cookies")
        print("2. 确保浏览器已登录Twitter")
        print("3. 检查网络连接")
