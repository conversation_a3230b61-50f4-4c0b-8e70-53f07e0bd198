#!/usr/bin/env python3
"""
超简单的cookies测试
"""

import asyncio
import json
import os
from twikit import Client

# 处理代理设置
def get_proxy():
    """获取代理配置"""
    proxy_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'all_proxy', 'ALL_PROXY']

    for var in proxy_vars:
        proxy_url = os.environ.get(var)
        if proxy_url:
            print(f"🔧 使用代理: {proxy_url}")
            # 转换socks代理格式
            if proxy_url.startswith('socks://'):
                proxy_url = proxy_url.replace('socks://', 'socks5://')
                print(f"🔄 转换为: {proxy_url}")
            return proxy_url
    return None

async def simple_test():
    print("🧪 超简单cookies测试")
    print("=" * 30)
    
    # 检查cookies文件
    if not os.path.exists('cookies.json'):
        print("❌ 找不到 cookies.json")
        return
    
    # 读取cookies
    with open('cookies.json', 'r') as f:
        cookies = json.load(f)

    print(f"✅ 读取cookies: {list(cookies.keys())}")
    
    # 创建客户端 - 使用系统代理
    proxy = get_proxy()
    if proxy:
        client = Client('en-US', proxy=proxy)
        print(f"✅ 创建客户端 (代理: {proxy})")
    else:
        client = Client('en-US')
        print("✅ 创建客户端 (无代理)")

    client.set_cookies(cookies)
    print("✅ 设置cookies完成")
    
    # 详细测试多个API
    print("🔍 开始详细测试...")

    # 测试1: 搜索推文
    try:
        print("\n� 测试1: 搜索推文...")
        tweets = await client.search_tweet('hello', 'Latest', count=1)

        if tweets:
            print(f"✅ 搜索成功! 找到 {len(tweets)} 条推文")
            print(f"   示例: {tweets[0].text[:50]}...")
        else:
            print("❌ 搜索无结果")

    except Exception as e:
        print(f"❌ 搜索推文失败: {e}")
        print(f"   错误类型: {type(e).__name__}")

    # 测试2: 获取用户信息
    try:
        print("\n👤 测试2: 获取用户信息...")
        user = await client.get_user_by_screen_name('twitter')
        print(f"✅ 获取用户成功: @{user.screen_name}")

    except Exception as e:
        print(f"❌ 获取用户失败: {e}")
        print(f"   错误类型: {type(e).__name__}")

    # 测试3: 尝试其他API
    try:
        print("\n🔧 测试3: 尝试设置API...")
        response, _ = await client.v11.settings()
        print(f"✅ 设置API成功: {type(response)}")

    except Exception as e:
        print(f"❌ 设置API失败: {e}")
        print(f"   错误类型: {type(e).__name__}")

    print("\n📊 测试完成")
    return False  # 暂时返回False，等待分析结果

if __name__ == "__main__":
    result = asyncio.run(simple_test())
    if result:
        print("\n🎉 Cookies有效! 可以使用browser_auth_scraper.py")
    else:
        print("\n💡 建议:")
        print("1. 重新导出cookies")
        print("2. 确保浏览器已登录Twitter")
        print("3. 检查网络连接")
