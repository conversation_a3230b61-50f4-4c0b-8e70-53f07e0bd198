# 🐦 安全Twitter推文抓取工具使用说明

## 📋 目录
- [功能特性](#功能特性)
- [安装要求](#安装要求)
- [快速开始](#快速开始)
- [安全设置](#安全设置)
- [使用方法](#使用方法)
- [防封号指南](#防封号指南)
- [常见问题](#常见问题)

## ✨ 功能特性

### 🛡️ 防封号保护
- **智能速率限制**: 自动遵守Twitter API限制
- **随机延迟**: 模拟人类行为模式
- **Cookies复用**: 减少登录频率
- **错误重试**: 智能处理网络异常
- **多账号轮换**: 分散请求压力

### 📊 数据处理
- **多格式导出**: JSON、CSV、Excel
- **统计分析**: 自动生成数据统计
- **时间过滤**: 支持日期范围筛选
- **批量处理**: 高效处理大量数据

### 🔧 易用性
- **交互式界面**: 简单的命令行交互
- **配置管理**: 安全的账号信息管理
- **进度显示**: 实时显示抓取进度
- **日志记录**: 详细的操作日志

## 📦 安装要求

### Python环境
```bash
Python 3.8+
```

### 依赖包
```bash
pip install twikit pandas openpyxl
```

### 项目文件
- `easy_scraper.py` - 主程序
- `config.json` - 配置文件（首次运行自动创建）

## 🚀 快速开始

### 1. 首次运行
```bash
python easy_scraper.py
```

首次运行会自动创建配置文件，请按提示输入：
- Twitter用户名
- 邮箱地址
- 密码

### 2. 开始抓取
程序会引导你输入：
- 目标用户ID或用户名
- 推文类型（Tweets/Replies/Media/Likes）
- 最大推文数量
- 输出格式（json/csv/excel）

### 3. 查看结果
抓取完成后，文件会保存在 `output` 目录中。

## ⚙️ 安全设置

### 速率限制配置
程序内置了Twitter的官方速率限制：

| 操作类型 | 限制 | 时间窗口 |
|---------|------|----------|
| 获取推文 | 50次 | 15分钟 |
| 用户信息 | 95次 | 15分钟 |
| 登录操作 | 187次 | 15分钟 |

### 安全参数
```json
{
  "scraping_settings": {
    "default_delay": 3.0,        // 默认延迟（秒）
    "min_delay": 2.0,            // 最小延迟
    "max_delay": 5.0,            // 最大延迟
    "max_retries": 3,            // 最大重试次数
    "batch_size": 20,            // 批处理大小
    "enable_random_delay": true, // 启用随机延迟
    "respect_rate_limits": true  // 遵守速率限制
  },
  "safety_settings": {
    "max_tweets_per_session": 1000,  // 单次最大推文数
    "max_users_per_day": 10,         // 每日最大用户数
    "cooldown_after_error": 300,     // 错误后冷却时间
    "auto_switch_account": true      // 自动切换账号
  }
}
```

## 📖 使用方法

### 基本抓取
```bash
python easy_scraper.py
```
按提示输入参数即可。

### 配置文件说明
`config.json` 包含以下部分：

#### 账号配置
```json
{
  "accounts": [
    {
      "name": "account1",
      "username": "your_username",
      "email": "<EMAIL>", 
      "password": "your_password",
      "cookies_file": "cookies_account1.json",
      "enabled": true
    }
  ]
}
```

#### 输出设置
```json
{
  "output_settings": {
    "default_format": "json",
    "output_directory": "output",
    "include_statistics": true,
    "compress_output": false
  }
}
```

### 输出文件说明

#### 推文数据文件
包含以下字段：
- `id`: 推文ID
- `text`: 推文内容
- `created_at`: 创建时间
- `user_*`: 用户信息
- `*_count`: 互动数据（点赞、转发、回复）
- `url`: 推文链接

#### 统计文件
包含：
- 总推文数
- 时间范围
- 互动统计
- 语言分布

## 🛡️ 防封号指南

### ⚠️ 重要提醒
使用本工具时请务必遵循以下原则：

### 1. 控制抓取频率
- **不要连续大量抓取**: 每次抓取后适当休息
- **分散时间段**: 避免在短时间内集中抓取
- **限制数量**: 单次抓取不超过1000条推文

### 2. 账号安全
- **使用小号**: 不要用主账号进行抓取
- **定期更换**: 如果可能，定期更换抓取账号
- **监控状态**: 注意账号是否有异常提示

### 3. 网络环境
- **稳定网络**: 使用稳定的网络连接
- **避免代理**: 尽量不使用不稳定的代理
- **IP轮换**: 如有条件可考虑IP轮换

### 4. 行为模拟
- **随机延迟**: 程序已内置随机延迟
- **模拟休息**: 长时间抓取时手动暂停
- **正常使用**: 偶尔正常浏览Twitter

### 5. 遵守规则
- **服务条款**: 遵守Twitter服务条款
- **法律法规**: 遵守当地法律法规
- **合理使用**: 仅用于合法的研究和分析

## ❓ 常见问题

### Q: 登录失败怎么办？
A: 
1. 检查用户名、邮箱、密码是否正确
2. 确认账号没有被锁定或限制
3. 尝试删除cookies文件重新登录
4. 检查网络连接是否正常

### Q: 抓取速度很慢？
A: 
1. 这是正常现象，为了防止封号
2. 可以适当调整延迟参数（不建议过小）
3. 分批次抓取，避免一次性抓取太多

### Q: 出现速率限制提示？
A: 
1. 等待提示的时间后再继续
2. 程序会自动处理速率限制
3. 如果频繁出现，考虑增加延迟时间

### Q: 数据不完整？
A: 
1. 检查目标用户的隐私设置
2. 确认账号有权限访问该用户
3. 某些推文可能已被删除

### Q: 如何添加多个账号？
A: 
1. 手动编辑 `config.json` 文件
2. 在 `accounts` 数组中添加新账号
3. 确保每个账号有不同的 `cookies_file`

## 📞 技术支持

如果遇到问题：
1. 查看日志文件 `twitter_scraper.log`
2. 检查配置文件格式是否正确
3. 确认网络连接和账号状态
4. 参考twikit官方文档

## ⚖️ 免责声明

本工具仅供学习和研究使用，使用者需要：
1. 遵守Twitter服务条款
2. 遵守当地法律法规
3. 承担使用风险
4. 合理使用，避免滥用

**请负责任地使用本工具！**
