# 🐦 安全Twitter推文抓取工具集

基于twikit库开发的安全、易用的Twitter推文抓取工具，具有完善的防封号保护机制。

## 🌟 核心特性

### 🛡️ 防封号保护
- **智能速率限制**: 严格遵守Twitter API限制
- **随机延迟**: 模拟真实用户行为
- **Cookies复用**: 减少登录频率，降低风险
- **错误重试**: 智能处理网络异常和临时限制
- **多账号支持**: 支持账号轮换分散请求

### 📊 强大功能
- **多种推文类型**: Tweets、Replies、Media、Likes
- **灵活导出**: JSON、CSV、Excel多种格式
- **统计分析**: 自动生成详细数据统计
- **批量处理**: 支持批量抓取多个用户
- **进度跟踪**: 实时显示抓取进度

### 🔧 易于使用
- **交互式界面**: 友好的命令行交互
- **配置管理**: 安全的账号信息管理
- **详细日志**: 完整的操作记录
- **错误处理**: 优雅的异常处理机制

## 📁 文件说明

### 核心文件
- `easy_scraper.py` - 主程序，交互式抓取工具
- `quick_scraper.py` - 命令行快速抓取工具
- `batch_scraper.py` - 批量抓取工具
- `main.py` - 原始抓取脚本（已优化）

### 配置文件
- `config.json` - 主配置文件（运行时自动创建）
- `config_example.json` - 配置文件示例
- `users_example.json` - 批量用户列表示例

### 文档
- `使用说明.md` - 详细使用说明
- `README_SCRAPER.md` - 项目说明（本文件）

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install twikit pandas openpyxl

# 确保Python 3.8+
python --version
```

### 2. 交互式使用
```bash
python easy_scraper.py
```
首次运行会引导创建配置文件，然后按提示操作即可。

### 3. 命令行使用
```bash
# 快速抓取
python quick_scraper.py elonmusk

# 自定义参数
python quick_scraper.py elonmusk --count 100 --format csv --type Replies
```

### 4. 批量抓取
```bash
# 创建用户列表示例
python batch_scraper.py --create-example

# 批量抓取
python batch_scraper.py users_example.json
```

## ⚙️ 配置说明

### 账号配置
```json
{
  "accounts": [
    {
      "name": "account1",
      "username": "your_username",
      "email": "<EMAIL>",
      "password": "your_password",
      "cookies_file": "cookies_account1.json",
      "enabled": true
    }
  ]
}
```

### 安全设置
```json
{
  "scraping_settings": {
    "default_delay": 3.0,           // 默认延迟
    "min_delay": 2.0,               // 最小延迟
    "max_delay": 5.0,               // 最大延迟
    "respect_rate_limits": true     // 遵守速率限制
  },
  "safety_settings": {
    "max_tweets_per_session": 1000, // 单次最大推文数
    "cooldown_after_error": 300     // 错误后冷却时间
  }
}
```

## 📊 输出格式

### 推文数据
每条推文包含：
- 基本信息：ID、内容、时间
- 用户信息：用户名、显示名
- 互动数据：点赞、转发、回复数
- 元数据：语言、链接等

### 统计报告
自动生成：
- 推文总数和时间范围
- 互动数据统计
- 语言分布分析
- 抓取性能指标

## 🛡️ 安全建议

### ⚠️ 重要提醒
1. **使用小号**: 不要用主账号进行抓取
2. **控制频率**: 避免短时间内大量抓取
3. **遵守限制**: 严格遵守速率限制
4. **监控状态**: 注意账号异常提示
5. **合法使用**: 仅用于合法研究目的

### 速率限制
| 操作 | 限制 | 窗口 |
|------|------|------|
| 获取推文 | 50次 | 15分钟 |
| 用户信息 | 95次 | 15分钟 |
| 登录 | 187次 | 15分钟 |

## 🔧 高级用法

### 自定义延迟
```bash
python quick_scraper.py username --delay 5.0
```

### 指定输出目录
```bash
python quick_scraper.py username --output /path/to/output
```

### 静默模式
```bash
python quick_scraper.py username --quiet
```

## 📝 使用示例

### 抓取特定用户的推文
```bash
python quick_scraper.py elonmusk --count 200 --format excel
```

### 抓取回复内容
```bash
python quick_scraper.py username --type Replies --count 100
```

### 批量抓取多个用户
1. 创建用户列表文件
2. 运行批量抓取：`python batch_scraper.py users.json`

## ❓ 常见问题

### Q: 登录失败？
A: 检查账号信息，确认网络连接，删除cookies重试

### Q: 速度太慢？
A: 这是防封号的必要措施，可适当调整延迟参数

### Q: 数据不完整？
A: 检查用户隐私设置和账号权限

### Q: 如何添加多账号？
A: 编辑config.json，在accounts数组中添加新账号

## 📞 技术支持

遇到问题时：
1. 查看日志文件 `twitter_scraper.log`
2. 检查配置文件格式
3. 参考使用说明文档
4. 确认网络和账号状态

## ⚖️ 免责声明

本工具仅供学习研究使用，使用者需要：
- 遵守Twitter服务条款
- 遵守当地法律法规
- 承担使用风险
- 负责任地使用

**请合理使用，避免滥用！**

## 🎯 项目优势

相比原始脚本，本工具集具有：
- ✅ 更完善的防封号机制
- ✅ 更友好的用户界面
- ✅ 更强大的批量处理能力
- ✅ 更详细的错误处理
- ✅ 更丰富的配置选项
- ✅ 更完整的文档说明

开始使用这个安全、强大的Twitter抓取工具吧！🚀
