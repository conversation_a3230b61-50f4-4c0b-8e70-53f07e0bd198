#!/usr/bin/env python3
"""
Twitter Cookies导出助手
帮助用户从浏览器导出cookies用于安全登录
"""

import json
import os
import sys
from datetime import datetime

def show_export_guide():
    """显示详细的cookies导出指南"""
    print("🍪 Twitter Cookies导出完整指南")
    print("=" * 60)
    print()
    
    print("📋 为什么使用Cookies登录？")
    print("✅ 更安全 - 无需保存密码")
    print("✅ 更稳定 - 减少登录频率")
    print("✅ 防封号 - 模拟真实浏览器行为")
    print("✅ 支持2FA - 无需处理双重验证")
    print()
    
    print("🔧 方法一: 使用浏览器扩展 (推荐)")
    print("-" * 40)
    print("Chrome浏览器:")
    print("1. 安装扩展: 'Get cookies.txt LOCALLY'")
    print("2. 访问 https://twitter.com 并登录")
    print("3. 点击扩展图标")
    print("4. 选择 'Export as JSON'")
    print("5. 保存为 cookies.json")
    print()
    
    print("Firefox浏览器:")
    print("1. 安装扩展: 'cookies.txt'")
    print("2. 访问 https://twitter.com 并登录")
    print("3. 点击扩展图标")
    print("4. 选择 'JSON' 格式")
    print("5. 保存文件")
    print()
    
    print("🔧 方法二: 手动导出")
    print("-" * 40)
    print("1. 在浏览器中登录 https://twitter.com")
    print("2. 按 F12 打开开发者工具")
    print("3. 进入 Application/Storage -> Cookies -> https://twitter.com")
    print("4. 复制重要cookies (见下方列表)")
    print("5. 按JSON格式保存")
    print()
    
    print("🔑 必需的Cookies字段 (仅需2个):")
    print("-" * 40)
    print("✅ auth_token: 认证令牌 (最重要)")
    print("✅ ct0: CSRF令牌 (防护令牌)")
    print()
    print("💡 提示: 只要有这两个字段就能正常登录！")
    print()

    print("📄 最简JSON格式:")
    print("-" * 40)
    print('''{
  "auth_token": "a1b2c3d4e5f6g7h8i9j0...",
  "ct0": "x1y2z3a4b5c6d7e8f9g0..."
}''')
    print()
    
    print("⚠️ 安全提醒:")
    print("-" * 40)
    print("🔒 Cookies包含敏感信息，请妥善保管")
    print("🔒 不要分享cookies文件给他人")
    print("🔒 定期更新cookies (建议每月一次)")
    print("🔒 使用完毕后可删除cookies文件")
    print()

def validate_cookies_file(file_path):
    """验证cookies文件"""
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            cookies = json.load(f)
        
        # 检查格式
        if isinstance(cookies, dict):
            # 键值对格式
            has_auth_token = 'auth_token' in cookies
            has_ct0 = 'ct0' in cookies

            # 统计额外的有用字段
            extra_fields = []
            useful_fields = ['guest_id', 'personalization_id', '__cf_bm', 'guest_id_ads', 'guest_id_marketing']
            for field in useful_fields:
                if field in cookies:
                    extra_fields.append(field)

            if has_auth_token:
                print("✅ 包含必需字段: auth_token")
                if has_ct0:
                    print("✅ 同时包含 ct0 (推荐)")
                else:
                    print("⚠️ 缺少 ct0，但 auth_token 通常足够")

                if extra_fields:
                    print(f"📋 额外字段: {', '.join(extra_fields)} (将被忽略)")
                else:
                    print("🎯 精简格式: 只有核心字段")

                print(f"📊 总共 {len(cookies)} 个cookies，使用 {'2' if has_ct0 else '1'} 个核心字段")
                return True
            else:
                print("❌ 缺少必需字段: auth_token")
                print("💡 至少需要 auth_token 才能登录")
                return False
                
        elif isinstance(cookies, list):
            # 数组格式
            cookie_names = []
            for cookie in cookies:
                if isinstance(cookie, dict) and 'name' in cookie:
                    cookie_names.append(cookie['name'])
            
            has_auth_token = 'auth_token' in cookie_names
            has_ct0 = 'ct0' in cookie_names

            if has_auth_token and has_ct0:
                print("✅ 完美！包含所有必需字段: auth_token, ct0")
                print(f"📊 总共 {len(cookies)} 个cookies")
                return True
            elif has_auth_token:
                print("⚠️ 只有 auth_token，缺少 ct0")
                print("💡 通常 auth_token 就足够了")
                return True
            else:
                print("❌ 缺少关键字段: auth_token")
                print(f"📋 当前字段: {', '.join(cookie_names[:10])}...")
                return False
        else:
            print("❌ 不支持的cookies格式")
            return False
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 文件读取错误: {e}")
        return False

def convert_cookies_format(input_file, output_file=None):
    """转换cookies格式"""
    if not output_file:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_converted.json"
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            cookies = json.load(f)
        
        if isinstance(cookies, list):
            # 数组格式转换为键值对格式
            converted = {}
            for cookie in cookies:
                if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                    converted[cookie['name']] = cookie['value']
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(converted, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 格式转换完成: {output_file}")
            return True
        else:
            print("✅ 格式已经正确，无需转换")
            return True
            
    except Exception as e:
        print(f"❌ 格式转换失败: {e}")
        return False

def create_sample_cookies():
    """创建示例cookies文件"""
    # 提供两种格式的示例
    minimal_cookies = {
        "auth_token": "请替换为真实的auth_token值",
        "ct0": "请替换为真实的ct0值"
    }

    complete_cookies = {
        "__cf_bm": "请替换为真实的__cf_bm值",
        "auth_token": "请替换为真实的auth_token值",
        "ct0": "请替换为真实的ct0值",
        "guest_id": "请替换为真实的guest_id值",
        "guest_id_ads": "请替换为真实的guest_id_ads值",
        "guest_id_marketing": "请替换为真实的guest_id_marketing值",
        "personalization_id": "请替换为真实的personalization_id值"
    }
    
    # 创建最小格式示例
    with open('cookies_minimal_sample.json', 'w', encoding='utf-8') as f:
        json.dump(minimal_cookies, f, indent=2, ensure_ascii=False)

    # 创建完整格式示例
    with open('cookies_complete_sample.json', 'w', encoding='utf-8') as f:
        json.dump(complete_cookies, f, indent=2, ensure_ascii=False)

    print("✅ 示例cookies文件已创建:")
    print("   📄 cookies_minimal_sample.json - 最小格式 (仅必需字段)")
    print("   📄 cookies_complete_sample.json - 完整格式 (推荐)")
    print("📝 请选择一个文件编辑，替换为真实的cookies值")
    print("💡 提示: 完整格式兼容性更好，推荐使用！")

def main():
    if len(sys.argv) < 2:
        print("🍪 Twitter Cookies导出助手")
        print("=" * 40)
        print("用法:")
        print("  python cookie_helper.py guide          # 显示导出指南")
        print("  python cookie_helper.py validate <文件> # 验证cookies文件")
        print("  python cookie_helper.py convert <文件>  # 转换cookies格式")
        print("  python cookie_helper.py sample         # 创建示例文件")
        print()
        print("示例:")
        print("  python cookie_helper.py guide")
        print("  python cookie_helper.py validate cookies.json")
        print("  python cookie_helper.py convert cookies_array.json")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == 'guide':
        show_export_guide()
    
    elif command == 'validate':
        if len(sys.argv) < 3:
            print("❌ 请指定cookies文件路径")
            print("用法: python cookie_helper.py validate <文件路径>")
            sys.exit(1)
        
        file_path = sys.argv[2]
        print(f"🔍 验证cookies文件: {file_path}")
        print("-" * 40)
        
        if validate_cookies_file(file_path):
            print("\n✅ Cookies文件验证通过，可以用于登录！")
        else:
            print("\n❌ Cookies文件验证失败，请检查格式")
    
    elif command == 'convert':
        if len(sys.argv) < 3:
            print("❌ 请指定cookies文件路径")
            print("用法: python cookie_helper.py convert <文件路径>")
            sys.exit(1)
        
        input_file = sys.argv[2]
        output_file = sys.argv[3] if len(sys.argv) > 3 else None
        
        print(f"🔄 转换cookies格式: {input_file}")
        print("-" * 40)
        
        if convert_cookies_format(input_file, output_file):
            print("\n✅ 格式转换完成！")
        else:
            print("\n❌ 格式转换失败")
    
    elif command == 'sample':
        print("📝 创建示例cookies文件...")
        print("-" * 40)
        create_sample_cookies()
    
    else:
        print(f"❌ 未知命令: {command}")
        print("支持的命令: guide, validate, convert, sample")

if __name__ == "__main__":
    main()
