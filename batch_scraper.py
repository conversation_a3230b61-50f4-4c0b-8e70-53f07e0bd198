#!/usr/bin/env python3
"""
批量Twitter推文抓取工具
支持从文件读取用户列表，批量抓取推文
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from easy_scraper import SafeTwitterScraper
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BatchScraper:
    def __init__(self, config_file: str = "config.json"):
        self.scraper = SafeTwitterScraper(config_file)
        self.results = []
        self.failed_users = []
    
    async def scrape_users_from_file(self, users_file: str, **kwargs):
        """从文件读取用户列表并批量抓取"""
        
        # 读取用户列表
        users = self._load_users_file(users_file)
        if not users:
            logger.error("没有找到有效的用户列表")
            return
        
        logger.info(f"📋 准备抓取 {len(users)} 个用户的推文")
        
        # 登录
        if not await self.scraper.login():
            logger.error("登录失败")
            return
        
        logger.info(f"✅ 登录成功: {self.scraper.current_account['name']}")
        
        # 批量抓取
        total_tweets = 0
        for i, user_config in enumerate(users, 1):
            try:
                username = user_config['username']
                user_kwargs = {**kwargs, **user_config}
                user_kwargs.pop('username', None)
                
                logger.info(f"🔄 [{i}/{len(users)}] 开始抓取 @{username}")
                
                tweets = await self.scraper.get_user_tweets_safe(
                    user_id=username,
                    **user_kwargs
                )
                
                if tweets:
                    # 保存数据
                    format_type = user_kwargs.get('format_type', 'json')
                    self.scraper.save_tweets(tweets, username, format_type)
                    
                    result = {
                        'username': username,
                        'tweet_count': len(tweets),
                        'status': 'success',
                        'timestamp': datetime.now().isoformat()
                    }
                    self.results.append(result)
                    total_tweets += len(tweets)
                    
                    logger.info(f"✅ @{username}: {len(tweets)} 条推文")
                else:
                    logger.warning(f"⚠️ @{username}: 没有获取到推文")
                    self.failed_users.append({
                        'username': username,
                        'reason': 'no_tweets',
                        'timestamp': datetime.now().isoformat()
                    })
                
                # 用户间延迟
                if i < len(users):
                    delay = user_kwargs.get('user_delay', 30)
                    logger.info(f"⏰ 等待 {delay} 秒后处理下一个用户...")
                    await asyncio.sleep(delay)
                    
            except Exception as e:
                logger.error(f"❌ @{username} 抓取失败: {e}")
                self.failed_users.append({
                    'username': username,
                    'reason': str(e),
                    'timestamp': datetime.now().isoformat()
                })
                
                # 错误后延迟
                await asyncio.sleep(60)
        
        # 生成报告
        self._generate_batch_report(total_tweets)
    
    def _load_users_file(self, users_file: str) -> list:
        """加载用户列表文件"""
        if not os.path.exists(users_file):
            logger.error(f"用户列表文件不存在: {users_file}")
            return []
        
        try:
            with open(users_file, 'r', encoding='utf-8') as f:
                if users_file.endswith('.json'):
                    data = json.load(f)
                    if isinstance(data, list):
                        return data
                    elif isinstance(data, dict) and 'users' in data:
                        return data['users']
                else:
                    # 纯文本文件，每行一个用户名
                    lines = f.read().strip().split('\n')
                    return [{'username': line.strip()} for line in lines if line.strip()]
        except Exception as e:
            logger.error(f"读取用户列表失败: {e}")
            return []
    
    def _generate_batch_report(self, total_tweets: int):
        """生成批量抓取报告"""
        report = {
            'batch_summary': {
                'total_users': len(self.results) + len(self.failed_users),
                'successful_users': len(self.results),
                'failed_users': len(self.failed_users),
                'total_tweets': total_tweets,
                'start_time': datetime.now().isoformat()
            },
            'successful_results': self.results,
            'failed_users': self.failed_users
        }
        
        # 保存报告
        output_dir = self.scraper.config['output_settings']['output_directory']
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(output_dir, f"batch_report_{timestamp}.json")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📊 批量抓取完成!")
        logger.info(f"   成功: {len(self.results)} 个用户")
        logger.info(f"   失败: {len(self.failed_users)} 个用户")
        logger.info(f"   总推文: {total_tweets} 条")
        logger.info(f"   报告文件: {report_file}")

def create_example_users_file():
    """创建示例用户列表文件"""
    example_users = {
        "users": [
            {
                "username": "elonmusk",
                "tweet_type": "Tweets",
                "max_tweets": 100,
                "format_type": "json"
            },
            {
                "username": "openai",
                "tweet_type": "Tweets", 
                "max_tweets": 200,
                "format_type": "csv"
            }
        ],
        "batch_settings": {
            "user_delay": 30,
            "format_type": "json"
        }
    }
    
    with open('users_example.json', 'w', encoding='utf-8') as f:
        json.dump(example_users, f, ensure_ascii=False, indent=2)
    
    print("✅ 示例用户列表文件已创建: users_example.json")

async def main():
    if len(sys.argv) < 2:
        print("用法:")
        print("  python batch_scraper.py <用户列表文件>")
        print("  python batch_scraper.py --create-example  # 创建示例文件")
        print("")
        print("用户列表文件格式:")
        print("  - JSON格式: 包含用户配置的JSON文件")
        print("  - 文本格式: 每行一个用户名")
        sys.exit(1)
    
    if sys.argv[1] == '--create-example':
        create_example_users_file()
        return
    
    users_file = sys.argv[1]
    
    # 检查配置文件
    if not os.path.exists('config.json'):
        print("❌ 配置文件不存在，请先运行 python easy_scraper.py 创建配置")
        sys.exit(1)
    
    try:
        batch_scraper = BatchScraper()
        await batch_scraper.scrape_users_from_file(
            users_file,
            tweet_type='Tweets',
            max_tweets=500,
            format_type='json',
            user_delay=30
        )
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        logger.error(f"批量抓取失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
