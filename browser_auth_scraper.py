#!/usr/bin/env python3
"""
浏览器授权Twitter抓取工具
专门为浏览器cookies登录设计的简化版本
"""

import asyncio
import json
import os
import sys
from datetime import datetime
import logging

# 处理代理设置
def get_proxy_config():
    """获取代理配置"""
    # 检查环境变量中的代理设置
    proxy_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'all_proxy', 'ALL_PROXY']

    print("🔍 检查代理环境变量...")
    for var in proxy_vars:
        proxy_url = os.environ.get(var)
        if proxy_url:
            print(f"🔧 检测到代理: {var}={proxy_url}")

            # 转换socks代理格式
            if proxy_url.startswith('socks://'):
                # 将 socks://127.0.0.1:7897 转换为 socks5://127.0.0.1:7897
                proxy_url = proxy_url.replace('socks://', 'socks5://')
                print(f"🔄 转换代理格式: {proxy_url}")
                return proxy_url
            elif proxy_url.startswith(('http://', 'https://', 'socks5://', 'socks4://')):
                return proxy_url
        else:
            print(f"   {var}: 未设置")

    print("❌ 未检测到任何代理设置")
    return None

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BrowserAuthScraper:
    """基于浏览器授权的Twitter抓取器"""
    
    def __init__(self, cookies_file: str = "cookies.json"):
        self.cookies_file = cookies_file
        self.client = None
        self.user_info = None
        self.is_logged_in = False
    
    async def login_with_cookies(self) -> bool:
        """使用cookies登录"""
        from twikit import Client

        if not os.path.exists(self.cookies_file):
            print(f"❌ Cookies文件不存在: {self.cookies_file}")
            print("请先运行: python cookie_helper.py guide")
            return False

        try:
            print(f"🍪 加载cookies文件: {self.cookies_file}")

            # 加载cookies
            with open(self.cookies_file, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)

            # 处理不同格式
            cookies = self._process_cookies(cookies_data)
            if not cookies:
                print("❌ Cookies格式无效")
                return False

            # 创建客户端 - 使用系统代理
            proxy_config = get_proxy_config()
            if proxy_config:
                print(f"🔧 创建客户端 (使用代理: {proxy_config})...")
                self.client = Client('en-US', proxy=proxy_config)
            else:
                print("🔧 创建客户端 (无代理)...")
                print("⚠️ 如果连接失败，可能需要设置代理")
                print("💡 尝试运行: export http_proxy=socks5://127.0.0.1:7897")
                self.client = Client('en-US')
            self.client.set_cookies(cookies)
            
            # 验证登录状态 - 直接尝试搜索推文
            print("🔍 验证登录状态...")

            try:
                print("🧪 测试推文搜索...")
                tweets = await self.client.search_tweet('hello', 'Latest', count=1)

                if tweets and len(tweets) > 0:
                    print("✅ Cookies验证成功! 可以正常访问Twitter")
                    self.user_info = {
                        'id': 'verified',
                        'screen_name': 'verified_user',
                        'name': 'Verified User',
                        'followers_count': 0,
                        'friends_count': 0
                    }
                    self.is_logged_in = True

                    # 保存更新后的cookies
                    self.client.save_cookies(self.cookies_file)
                    return True
                else:
                    print("❌ 搜索推文无结果，cookies可能无效")
                    return False

            except Exception as e:
                error_msg = str(e)
                print(f"❌ 推文搜索失败: {e}")
                print(f"🔍 错误类型: {type(e).__name__}")

                # 详细的错误分析
                if 'ConnectTimeout' in error_msg or 'timeout' in error_msg.lower():
                    print("� 网络连接超时!")
                    print("�💡 解决方案:")
                    print("   1. 检查网络连接")
                    print("   2. 设置代理: export http_proxy=socks5://127.0.0.1:7897")
                    print("   3. 或运行: http_proxy=socks5://127.0.0.1:7897 python browser_auth_scraper.py")
                elif 'proxy' in error_msg.lower():
                    print("🚨 代理相关错误!")
                    print("💡 检查代理设置是否正确")
                elif '401' in error_msg or '403' in error_msg:
                    print("🚨 认证错误!")
                    print("💡 Cookies可能已过期，请重新导出")
                else:
                    print("💡 其他可能原因:")
                    print("   1. Cookies已过期")
                    print("   2. 网络连接问题")
                    print("   3. Twitter API限制")

                return False
                
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            return False
    
    def _process_cookies(self, cookies_data) -> dict:
        """处理cookies数据"""
        if isinstance(cookies_data, dict):
            return cookies_data
        elif isinstance(cookies_data, list):
            # 数组格式转换
            cookies = {}
            for item in cookies_data:
                if isinstance(item, dict) and 'name' in item and 'value' in item:
                    cookies[item['name']] = item['value']
            return cookies
        return {}
    
    async def _get_user_info(self) -> dict:
        """获取用户信息 - 简化版本"""
        # 现在这个方法主要用于向后兼容
        return self.user_info if hasattr(self, 'user_info') and self.user_info else None
    
    async def scrape_user_tweets(self, username: str, max_tweets: int = 100) -> list:
        """抓取用户推文"""
        if not self.is_logged_in:
            print("❌ 请先登录")
            return []
        
        try:
            print(f"📋 开始抓取 @{username} 的推文...")
            
            # 获取用户信息
            try:
                user = await self.client.get_user_by_screen_name(username)
            except:
                user = await self.client.get_user_by_id(username)
            
            # 获取推文
            tweets = await self.client.get_user_tweets(user.id, 'Tweets', count=20)
            tweets_data = []
            
            count = 0
            while tweets and count < max_tweets:
                for tweet in tweets:
                    if count >= max_tweets:
                        break
                    
                    tweet_data = {
                        'id': tweet.id,
                        'text': tweet.text,
                        'created_at': tweet.created_at,
                        'user_name': user.name,
                        'user_screen_name': user.screen_name,
                        'retweet_count': getattr(tweet, 'retweet_count', 0),
                        'favorite_count': getattr(tweet, 'favorite_count', 0),
                        'url': f"https://twitter.com/{user.screen_name}/status/{tweet.id}"
                    }
                    
                    tweets_data.append(tweet_data)
                    count += 1
                
                # 获取下一页
                if count < max_tweets:
                    try:
                        await asyncio.sleep(2)  # 安全延迟
                        tweets = await tweets.next()
                    except:
                        break
            
            print(f"✅ 抓取完成，共获取 {len(tweets_data)} 条推文")
            return tweets_data
            
        except Exception as e:
            print(f"❌ 抓取失败: {e}")
            return []
    
    def save_tweets(self, tweets_data: list, username: str, format_type: str = 'json'):
        """保存推文数据"""
        if not tweets_data:
            print("⚠️ 没有数据可保存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if format_type == 'json':
            filename = f"{username}_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(tweets_data, f, ensure_ascii=False, indent=2, default=str)
            print(f"💾 已保存到: {filename}")
        
        elif format_type == 'txt':
            filename = f"{username}_{timestamp}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"@{username} 的推文合集\n")
                f.write(f"抓取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总计: {len(tweets_data)} 条推文\n")
                f.write("=" * 50 + "\n\n")
                
                for i, tweet in enumerate(tweets_data, 1):
                    f.write(f"推文 {i}:\n")
                    f.write(f"时间: {tweet['created_at']}\n")
                    f.write(f"内容: {tweet['text']}\n")
                    f.write(f"链接: {tweet['url']}\n")
                    f.write("-" * 30 + "\n\n")
            
            print(f"💾 已保存到: {filename}")

async def interactive_mode():
    """交互式模式"""
    print("🍪 浏览器授权Twitter抓取工具")
    print("=" * 50)

    # 直接使用 cookies.json 文件
    cookies_file = "cookies.json"

    if not os.path.exists(cookies_file):
        print(f"\n❌ 找不到 {cookies_file} 文件")
        print("📖 请先创建cookies.json文件，运行以下命令查看指南:")
        print("   python cookie_helper.py guide")
        print("   python cookie_helper.py sample")
        return
    
    # 创建抓取器并登录
    scraper = BrowserAuthScraper(cookies_file)
    
    print(f"\n🔐 正在登录...")
    if not await scraper.login_with_cookies():
        return
    
    # 显示用户信息
    user_info = scraper.user_info
    print(f"\n👤 当前用户信息:")
    print(f"   用户名: @{user_info['screen_name']}")
    print(f"   显示名: {user_info['name']}")
    print(f"   粉丝数: {user_info['followers_count']:,}")
    print(f"   关注数: {user_info['friends_count']:,}")
    
    # 获取抓取参数
    print(f"\n📝 请输入抓取参数:")
    target_user = input("目标用户名: ").strip()
    if not target_user:
        print("❌ 用户名不能为空")
        return
    
    try:
        max_tweets = int(input("最大推文数量 [100]: ").strip() or "100")
    except ValueError:
        max_tweets = 100
    
    format_type = input("输出格式 (json/txt) [json]: ").strip() or "json"
    
    # 开始抓取
    print(f"\n🚀 开始抓取 @{target_user} 的推文...")
    tweets = await scraper.scrape_user_tweets(target_user, max_tweets)
    
    if tweets:
        scraper.save_tweets(tweets, target_user, format_type)
        print(f"\n✅ 抓取完成！")
    else:
        print(f"\n⚠️ 没有获取到推文")

async def command_mode():
    """命令行模式"""
    if len(sys.argv) < 3:
        print("用法: python browser_auth_scraper.py <用户名> <推文数量>")
        print("示例: python browser_auth_scraper.py elonmusk 100")
        print("注意: 请确保当前目录有 cookies.json 文件")
        return

    username = sys.argv[1]
    max_tweets = int(sys.argv[2])
    cookies_file = "cookies.json"  # 固定使用 cookies.json
    
    scraper = BrowserAuthScraper(cookies_file)
    
    if await scraper.login_with_cookies():
        tweets = await scraper.scrape_user_tweets(username, max_tweets)
        if tweets:
            scraper.save_tweets(tweets, username)

async def main():
    try:
        if len(sys.argv) == 1:
            # 交互式模式
            await interactive_mode()
        else:
            # 命令行模式
            await command_mode()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 程序错误: {e}")

if __name__ == "__main__":
    asyncio.run(main())
